---
description:  这是一个规则文件，用于帮助 AI 理解代码库和遵循项目约定。
globs:
alwaysApply: true
type: "always_apply"
---
# project-rule

## 1. 项目概述 (Project Overview)

- **项目名称**: FaciShare 导航 (fs-oss-navigation)
- **核心价值**: 一个现代化的、功能丰富的企业级静态导航页面。
- **技术栈**: 纯原生 JavaScript (ES6+), HTML5, CSS3。**注意：这是一个纯前端项目，不涉及任何后端语言（如Java）或框架。**

## 2. 架构与设计原则 (Architecture & Design Principles)

- **架构模式**: 这是一个数据驱动的静态网站。核心逻辑由 JavaScript 动态加载和渲染 `nav/data/` 目录下的 JSON 数据。它不依赖任何现代前端框架（如 Vue, React）。
- **设计原则**:
    - **配置驱动**: 核心行为（如时间提示、应用设置）由 `nav/data/appconfig.json` 控制。
    - **数据分离**: 导航链接数据与应用逻辑分离，存储在多个 JSON 文件中 (`firstshare.json`, `tools-site.json` 等)。
    - **模块化JS**: 功能被拆分到不同的 JS 文件中 (e.g., `search.js`, `theme.js`, `sidebar.js`)，遵循关注点分离原则。

## 3. 关键文件与目录指南 (Key File & Directory Guide)

- `/index.html`: 主应用入口页面。
- `/npve.html`: 一个独立的可视化编辑器，用于修改导航数据。
- `/nav/`: **应用核心目录**
    - `js/`: 存放所有 JavaScript 逻辑。
        - `app.js`: 应用主逻辑和初始化脚本。
        - `config.js`: 配置加载与管理。
        - `search.js`: 搜索功能实现。
        - `theme.js`: 主题切换逻辑。
        - `utils.js`: 通用工具函数。
    - `data/`: **数据中心**
        - `appconfig.json`: 全局配置文件。
        - `*.json`: 导航站点数据源。修改导航内容主要就是修改这些文件。
    - `css/`: 样式文件。
- `/docs/`: 存放所有项目Markdown文档，是理解项目功能细节的重要参考。

## 4. AI 交互核心指令 (Core Instructions for AI Interaction)

- **理解数据源**: 当需要修改导航链接时，应定位到 `nav/data/` 目录下的相关 JSON 文件进行编辑。
- **理解配置**: 当需要调整应用行为时，应首先查阅并修改 `nav/data/appconfig.json`。
- **遵循模块化**: 当添加或修改功能时，应在 `nav/js/` 目录下找到对应的模块文件，或创建新文件，并在 `app.js` 或 `index.html` 中进行集成。
- **查阅文档**: 在进行任何复杂操作前，优先查阅 `/docs/` 目录下的相关文档。