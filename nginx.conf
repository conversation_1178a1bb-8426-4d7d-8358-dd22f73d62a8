server {
    listen       80;
    server_name  localhost;

    # 网站文件根目录
    root   /usr/share/nginx/html;

    # 默认首页文件
    index  index.html index.htm;

    # 1. 对 index.html 精确匹配，指示浏览器每次都必须重新验证
    #    add_header 'Cache-Control' 'no-cache' 是一种常用的策略
    location = /index.html {
        add_header 'Cache-Control' 'no-cache';
    }

    # 2. 对其他静态资源设置永久缓存
    #    因为它们的文件名已经通过查询参数实现了版本化
    location ~* \.(?:css|js|jpg|jpeg|gif|png|ico|svg|woff|woff2|eot|ttf|json)$ {
        expires 365d;
        # 'public' 表示任何缓存（浏览器、CDN）都可以缓存它
        # 'immutable' 告诉浏览器这个文件内容永远不会改变，不需要重新验证
        add_header 'Cache-Control' 'public, immutable';
    }

    # 3. 其他所有请求的默认处理规则
    location / {
        try_files $uri $uri/ =404;
    }
}
