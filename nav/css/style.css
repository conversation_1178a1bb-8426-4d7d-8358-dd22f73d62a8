/* CSS变量定义 */
:root {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    
    --background-color: #ffffff;
    --surface-color: #f8fafc;
    --surface-hover: #f1f5f9;
    --card-background: #ffffff;
    
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    
    --sidebar-width: 280px;
    --navbar-height: 64px;
    
    --transition-fast: 0.15s ease;
    --transition-normal: 0.25s ease;
    --transition-slow: 0.35s ease;
}

/* 基础样式重置 */
* {
    box-sizing: border-box;
}

/**
 * FONT AWESOME OVERRIDE FIX
 * 解决全局样式覆盖 Font Awesome 字体的问题
 * 修复图标显示为空白方框的 BUG
 */
[class^="fa-"], [class*=" fa-"],
.fas, .fab, .far, .fal, .fad, .fa {
    /* 核心修复：强制使用 Font Awesome 字体 */
    font-family: "Font Awesome 7 Free" !important;

    /* 可选修复：恢复标准的显示方式和大小 */
    display: inline-block !important;
    width: auto !important;
    height: auto !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;

    /* 确保图标正确对齐 */
    vertical-align: baseline !important;
    line-height: 1 !important;
    text-align: center !important;
}

/* 针对品牌图标的特殊字体覆盖 */
.fab, .fa-brands {
    font-family: "Font Awesome 7 Brands" !important;
}

/* 针对 Font Awesome 5 兼容性 */
.fa-solid {
    font-family: "Font Awesome 7 Free" !important;
    font-weight: 900 !important;
}

.fa-regular {
    font-family: "Font Awesome 7 Free" !important;
    font-weight: 400 !important;
}

/* 确保在 flex 容器中的图标也能正确显示 */
.action-btn .fas,
.action-btn .far,
.action-btn .fab,
.action-btn .fa,
.search-filter-btn .fas,
.dropdown-item .fas,
.theme-option .fas {
    font-family: "Font Awesome 7 Free" !important;
    display: inline-block !important;
    width: auto !important;
    height: auto !important;
}

body {
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    transition: background-color var(--transition-normal), color var(--transition-normal);
    line-height: 1.6;
}

/* 通用动作按钮样式 */
.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    font-size: 16px;
}

.action-btn:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.action-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-color);
}

/* 主题选择器按钮特殊样式 */
.theme-selector-btn {
    width: auto;
    min-width: 40px;
    padding: 8px 12px;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.theme-selector-btn:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

/* 顶部导航栏 */
.navbar-custom {
    height: var(--navbar-height);
    background-color: var(--card-background);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.navbar-brand-custom {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.logo {
    width: 32px;
    height: 32px;
    transition: all 0.3s ease;
    vertical-align: middle;
    margin-right: 4px;
}

.logo:hover {
    transform: scale(1.05);
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 导航栏动作区域 */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0; /* 防止动作区域被压缩 */
}

/* 移动端菜单按钮基础样式 */
.mobile-menu-btn {
    display: none; /* 默认隐藏，在移动端显示 */
    position: relative;
    overflow: hidden;
}

.mobile-menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    opacity: 0;
    transition: opacity var(--transition-fast);
    border-radius: var(--radius-md);
}

.mobile-menu-btn:hover::before {
    opacity: 0.1;
}

/* 移动端菜单按钮激活状态 */
.mobile-menu-btn.active {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.mobile-menu-btn.active::before {
    opacity: 0.2;
}

/* 视图切换按钮 */
.view-toggle-btn {
    position: relative;
    overflow: hidden;
    width: auto;
    min-width: 40px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.view-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity var(--transition-fast);
    border-radius: var(--radius-md);
}

.view-toggle-btn:hover::before {
    opacity: 0.1;
}

.view-toggle-btn.compact-mode {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.view-toggle-btn.compact-mode::before {
    opacity: 0.2;
}

.view-toggle-btn i {
    transition: transform var(--transition-fast);
}

.view-toggle-btn:hover i {
    transform: scale(1.1);
}

.view-mode-text {
    margin-left: 6px;
    font-size: 12px;
    font-weight: 500;
}

/* 搜索容器 */
.search-container {
    position: relative;
    flex: 1;
    max-width: 500px;
    margin: 0 40px;
}

.search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    height: 40px;
    padding: 0 120px 0 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 14px;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: var(--card-background);
}

/* 筛选状态样式 */
.search-wrapper.filtered {
    border-color: var(--warning-color);
    background-color: rgba(245, 158, 11, 0.05);
}

.search-wrapper.filtered .search-input {
    background-color: rgba(245, 158, 11, 0.05);
}

.search-wrapper.filtered .search-input::placeholder {
    color: var(--warning-color);
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-icon {
    position: absolute;
    left: 12px;
    color: var(--text-muted);
    font-size: 14px;
    z-index: 1;
}

.search-actions {
    position: absolute;
    right: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.search-filter-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: none;
    border-radius: var(--radius-sm);
    background-color: transparent;
    color: var(--text-muted);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.search-filter-btn:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.search-filter-btn.active {
    background-color: var(--primary-color);
    color: white;
}

/* 筛选状态指示器 */
.search-filter-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background-color: var(--warning-color);
    border-radius: 50%;
    border: 2px solid var(--card-background);
    display: none;
    animation: pulse 2s infinite;
}

.search-filter-btn.has-filters .search-filter-indicator {
    display: block;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
    }
    70% {
        box-shadow: 0 0 0 4px rgba(245, 158, 11, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
    }
}

.search-shortcut {
    background-color: var(--surface-hover);
    color: var(--text-muted);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 500;
}

/* 搜索筛选 */
.search-filters {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1002;
    margin-top: 4px;
    padding: 16px;
}

.search-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.search-filters-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.clear-filters-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.clear-filters-btn:hover {
    background-color: var(--surface-hover);
}

.search-filters-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    padding: 8px 32px 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 14px;
    transition: all var(--transition-fast);
    cursor: pointer;
    /* 默认下拉箭头 */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2394a3b8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.filter-select:hover {
    border-color: var(--border-hover);
    background-color: var(--surface-hover);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background-color: var(--card-background);
}

.filter-select option {
    background-color: var(--card-background);
    color: var(--text-primary);
    padding: 8px 12px;
}

/* 自定义下拉选择器 */
.custom-select {
    position: relative;
    width: 100%;
}

.custom-select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 14px;
    cursor: pointer;
    transition: all var(--transition-fast);
    user-select: none;
}

.custom-select-trigger:hover {
    border-color: var(--border-hover);
    background-color: var(--surface-hover);
}

.custom-select.open .custom-select-trigger {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background-color: var(--card-background);
}

.custom-select-text {
    flex: 1;
    text-align: left;
}

.custom-select-arrow {
    font-size: 12px;
    color: var(--text-muted);
    transition: transform var(--transition-fast);
}

.custom-select.open .custom-select-arrow {
    transform: rotate(180deg);
}

.custom-select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1003;
    margin-top: 4px;
    max-height: 300px;
    overflow-y: auto;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all var(--transition-fast);
}

.custom-select.open .custom-select-options {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.custom-select-option {
    padding: 10px 12px;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
    color: var(--text-primary);
}

.custom-select-option:last-child {
    border-bottom: none;
}

.custom-select-option:hover {
    background-color: var(--surface-hover);
}

.custom-select-option.active {
    background-color: var(--primary-color);
    color: white;
}

.custom-select-option.active:hover {
    background-color: var(--primary-hover);
}

.tag-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.tag-filter {
    padding: 4px 8px;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 12px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.tag-filter:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

.tag-filter.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 搜索结果 */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
    margin-top: 4px;
}

.search-result-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-result-item:hover {
    background-color: var(--surface-color);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.search-result-desc {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.search-result-category {
    font-size: 11px;
    color: var(--text-muted);
    margin-top: 4px;
}

.search-result-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 6px;
}

.search-result-tag {
    display: inline-block;
    padding: 2px 6px;
    font-size: 10px;
    background-color: var(--accent-color-alpha);
    color: var(--accent-color);
    border-radius: var(--radius-sm);
    border: 1px solid transparent;
    transition: all var(--transition-fast);
}

.search-result-tag.highlighted {
    background-color: var(--accent-color);
    color: var(--surface-color);
    font-weight: 500;
}

/* 新的精确高亮样式 - 主题适配 */
.highlight {
    background-color: var(--highlight-bg);
    color: var(--highlight-text);
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 500;
    border: 1px solid var(--highlight-border);
    transition: all var(--transition-fast) ease;
}

/* mark标签主题适配 - 修复管道搜索高亮问题 */
mark {
    background-color: var(--highlight-bg);
    color: var(--highlight-text);
    padding: 1px 2px;
    border-radius: 2px;
    border: 1px solid var(--highlight-border);
    transition: all var(--transition-fast) ease;
}

/* 管道搜索中的mark标签分级样式 */
mark.pipeline-highlight-1 {
    background-color: var(--pipeline-highlight-1-bg);
    color: var(--pipeline-highlight-1-text);
    border: 1px solid var(--pipeline-highlight-1-border);
}

mark.pipeline-highlight-2 {
    background-color: var(--pipeline-highlight-2-bg);
    color: var(--pipeline-highlight-2-text);
    border: 1px solid var(--pipeline-highlight-2-border);
}

mark.pipeline-highlight-3 {
    background-color: var(--pipeline-highlight-3-bg);
    color: var(--pipeline-highlight-3-text);
    border: 1px solid var(--pipeline-highlight-3-border);
}

/* 搜索匹配类型徽章 */
.search-match-badge {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    padding: 1px 4px;
    font-size: 9px;
    border-radius: var(--radius-sm);
    margin-left: 6px;
    font-weight: 500;
    opacity: 0.8;
}

.search-match-badge i {
    font-size: 8px;
}

.badge-primary { background-color: #007bff; color: white; }
.badge-secondary { background-color: #6c757d; color: white; }
.badge-success { background-color: #28a745; color: white; }
.badge-info { background-color: #17a2b8; color: white; }
.badge-warning { background-color: #ffc107; color: #212529; }
.badge-muted { background-color: #e9ecef; color: #6c757d; }
.badge-pipeline { background-color: #8b5cf6; color: white; }

/* 管道搜索样式 */
.pipeline-search-info {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: var(--radius-lg);
    padding: 12px 16px;
    margin-bottom: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pipeline-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 13px;
}

.pipeline-header i {
    color: #8b5cf6;
    font-size: 14px;
}

.pipeline-final-count {
    margin-left: auto;
    background-color: #8b5cf6;
    color: white;
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 500;
}

.pipeline-steps {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.pipeline-step {
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: var(--radius-md);
    padding: 6px 10px;
    font-size: 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.pipeline-step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background-color: #8b5cf6;
    color: white;
    border-radius: 50%;
    font-size: 10px;
    font-weight: 600;
}

.pipeline-step-term {
    color: var(--text-primary);
    font-weight: 500;
}

.pipeline-step-count {
    color: var(--text-muted);
    font-size: 11px;
}

.pipeline-arrow {
    color: #94a3b8;
    font-size: 10px;
    margin: 0 4px;
}

/* 管道搜索结果项样式 - 主题适配 */
.search-result-item.pipeline-result {
    border-left: 3px solid var(--primary-color);
    background: linear-gradient(90deg, var(--highlight-bg) 0%, transparent 100%);
}

.search-result-item.pipeline-result:hover {
    background: linear-gradient(90deg, var(--highlight-bg) 0%, var(--surface-color) 100%);
}

/* 管道搜索高亮样式 - 主题适配 */
.pipeline-highlight-1 {
    background-color: var(--pipeline-highlight-1-bg);
    color: var(--pipeline-highlight-1-text);
    padding: 1px 2px;
    border-radius: 2px;
    border: 1px solid var(--pipeline-highlight-1-border);
    transition: all var(--transition-fast) ease;
}

.pipeline-highlight-2 {
    background-color: var(--pipeline-highlight-2-bg);
    color: var(--pipeline-highlight-2-text);
    padding: 1px 2px;
    border-radius: 2px;
    border: 1px solid var(--pipeline-highlight-2-border);
    transition: all var(--transition-fast) ease;
}

.pipeline-highlight-3 {
    background-color: var(--pipeline-highlight-3-bg);
    color: var(--pipeline-highlight-3-text);
    padding: 1px 2px;
    border-radius: 2px;
    border: 1px solid var(--pipeline-highlight-3-border);
    transition: all var(--transition-fast) ease;
}

/* 管道搜索提示 */
.pipeline-hint {
    font-size: 11px;
    color: var(--text-muted);
    margin-top: 4px;
    font-style: italic;
}

/* Markdown 模态框 */
.markdown-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow-y: auto;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.markdown-modal.show {
    opacity: 1;
    visibility: visible;
}

.markdown-modal.show .markdown-modal-content {
    transform: scale(1);
    opacity: 1;
}

.markdown-modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.markdown-modal-content {
    position: relative;
    background-color: var(--card-background);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s ease;
}

.markdown-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.markdown-modal-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.markdown-modal-close {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-muted);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.markdown-modal-close:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.markdown-modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

.markdown-content {
    color: var(--text-primary);
    line-height: 1.6;
}

/* Markdown 内容样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    color: var(--text-primary);
}

.markdown-content h1 {
    font-size: 28px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.markdown-content h2 {
    font-size: 24px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 6px;
}

.markdown-content h3 {
    font-size: 20px;
}

.markdown-content h4 {
    font-size: 18px;
}

.markdown-content p {
    margin-bottom: 16px;
    color: var(--text-secondary);
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 16px;
    padding-left: 24px;
}

.markdown-content li {
    margin-bottom: 4px;
    color: var(--text-secondary);
}

.markdown-content code {
    background-color: var(--surface-color);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: var(--accent-color);
    border: 1px solid var(--border-color);
}

.markdown-content pre {
    background-color: var(--surface-color);
    padding: 16px;
    border-radius: var(--radius-md);
    overflow-x: auto;
    margin-bottom: 16px;
    border: 1px solid var(--border-color);
}

.markdown-content pre code {
    background: none;
    padding: 0;
    border: none;
    color: var(--text-primary);
}

.markdown-content blockquote {
    border-left: 4px solid var(--accent-color);
    padding-left: 16px;
    margin: 16px 0;
    color: var(--text-muted);
    font-style: italic;
}

.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;
}

.markdown-content th,
.markdown-content td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid var(--border-color);
}

.markdown-content th {
    background-color: var(--surface-color);
    font-weight: 600;
    color: var(--text-primary);
}

.markdown-content td {
    color: var(--text-secondary);
}

.markdown-content a {
    color: var(--accent-color);
    text-decoration: none;
}

.markdown-content a:hover {
    text-decoration: underline;
}

.markdown-content hr {
    border: none;
    height: 1px;
    background-color: var(--border-color);
    margin: 24px 0;
}

/* 主容器 */
.main-container {
    display: flex;
    margin-top: var(--navbar-height);
    min-height: calc(100vh - var(--navbar-height));
}

/* 侧边栏样式优化 */
.sidebar {
    width: var(--sidebar-width);
    height: calc(100vh - var(--navbar-height));
    background-color: var(--card-background);
    border-right: 1px solid var(--border-color);
    position: fixed;
    top: var(--navbar-height);
    left: 0;
    z-index: 999; /* 提高z-index确保在遮罩层之上 */
    overflow-y: auto;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

/* 分类导航区域 */
.category-nav {
    padding: 4px 8px 16px 8px;
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    margin: 0;
    position: relative;
}

.category-item + .category-item {
    margin-top: 1px;
}

.category-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    color: var(--text-secondary);
    text-decoration: none;
    /* 移除过渡效果以提升性能 */
    position: relative;
    font-size: 13px;
    border-radius: var(--radius-md);
    margin: 0 8px;
    min-height: 36px;
    cursor: pointer;
    border: 1px solid transparent;
}

.category-link:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
    border-color: var(--border-color);
    /* 移除transform动画 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.category-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.15);
    /* 移除transform动画 */
    font-weight: 500;
}

.category-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
    /* 移除transform过渡效果 */
    flex-shrink: 0;
}

/* 移除图标缩放动画 */
.category-link:hover .category-icon {
    /* transform: scale(1.1); */
}

.category-arrow {
    font-size: 9px;
    /* 移除过渡效果 */
    padding: 4px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    margin-left: auto;
    opacity: 0.6;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.category-arrow:hover {
    background-color: rgba(0, 0, 0, 0.05);
    opacity: 1;
    /* 移除缩放动画 */
}

.category-link.active .category-arrow:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

.category-link.expanded .category-arrow {
    /* 即时旋转，无动画 */
    transform: rotate(90deg);
    opacity: 1;
}

.category-name {
    font-weight: 500;
    flex: 1;
    transition: color var(--transition-fast);
    line-height: 1.2;
}

.category-name:hover {
    color: var(--primary-color);
}

.category-link.active .category-name:hover {
    color: white;
}

.category-count {
    font-size: 10px;
    background-color: var(--surface-hover);
    color: var(--text-muted);
    padding: 1px 5px;
    border-radius: 10px;
    font-weight: 500;
    margin-left: 4px;
    /* 移除过渡效果 */
    min-width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.category-link:hover .category-count {
    background-color: var(--primary-color);
    color: white;
    /* 移除缩放动画 */
}

.category-link.active .category-count {
    background-color: rgba(255, 255, 255, 0.25);
    color: white;
}

/* 子分类列表优化 - 无动画版本（性能优化） */
.subcategory-list {
    list-style: none;
    padding: 0;
    margin: 0;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.01),
        rgba(59, 130, 246, 0.005));
    margin-left: 20px;
    margin-right: 8px;
    border-radius: var(--radius-sm);
    overflow: hidden;
    position: relative;
    /* 完全移除动画，即时显示/隐藏 */
    display: none;
    padding: 4px 0;
}

/* 展开状态的子分类列表 - 即时显示 */
.subcategory-list.expanded {
    display: block;
}

/* 垂直主干连接线 - 从父级延伸到所有子级 */
.subcategory-list::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    width: 1px;
    background: linear-gradient(to bottom,
        var(--text-muted) 0%,
        var(--text-muted) 90%,
        transparent 100%);
    opacity: 0.4;
    transition: opacity 0.3s ease;
}

.subcategory-list[style*="block"]::before {
    opacity: 0.6;
}

.subcategory-item {
    position: relative;
}

.subcategory-list .category-link {
    padding: 6px 12px 6px 16px;
    color: var(--text-muted);
    font-size: 12px;
    margin: 0 4px;
    border-radius: var(--radius-sm);
    min-height: 28px;
    background: transparent;
    border: 1px solid transparent;
    position: relative;
}

/* 水平分支连接线 - 连接每个子级项目 */
.subcategory-list .category-link::before {
    content: '';
    position: absolute;
    left: -16px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 1px;
    background-color: var(--text-muted);
    opacity: 0.4;
    /* 移除过渡效果 */
}

/* 分支节点指示器 */
.subcategory-list .category-link::after {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 3px;
    background-color: var(--text-muted);
    border-radius: 50%;
    opacity: 0.5;
    /* 移除过渡效果 */
}

.subcategory-list .category-link:hover {
    background-color: rgba(59, 130, 246, 0.08);
    color: var(--text-primary);
    border-color: rgba(59, 130, 246, 0.2);
    /* 移除transform动画 */
}

.subcategory-list .category-link:hover::before {
    background-color: var(--primary-color);
    opacity: 0.7;
}

.subcategory-list .category-link:hover::after {
    background-color: var(--primary-color);
    opacity: 0.8;
    /* 移除缩放动画 */
    transform: translateY(-50%);
}

.subcategory-list .category-link.active {
    background: linear-gradient(135deg,
        var(--primary-color),
        var(--primary-hover));
    color: white;
    /* 移除transform动画 */
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
}

.subcategory-list .category-link.active::before {
    background-color: white;
    opacity: 0.9;
}

.subcategory-list .category-link.active::after {
    background-color: white;
    opacity: 1;
    /* 移除缩放动画 */
    transform: translateY(-50%);
}

.subcategory-list .category-icon {
    font-size: 12px;
    width: 14px;
    opacity: 0.8;
}

.subcategory-list .category-name {
    font-weight: 400;
    font-size: 12px;
}

.subcategory-list .category-count {
    font-size: 9px;
    padding: 1px 4px;
    min-width: 14px;
}

/* 三级分类支持 - 嵌套树状结构 */
.subcategory-list .subcategory-list {
    margin-left: 16px;
}

.subcategory-list .subcategory-list::before {
    left: -6px;
    opacity: 0.3;
}

/* 分类分隔线 */
.category-divider {
    margin: 12px 0;
    padding: 0;
    list-style: none;
}

.category-separator {
    border: none;
    height: 1px;
    background-color: var(--border-color);
    margin: 0 16px;
}

.subcategory-list .subcategory-list .category-link::before {
    left: -12px;
    width: 6px;
}

.subcategory-list .subcategory-list .category-link::after {
    left: -6px;
    width: 2px;
    height: 2px;
}

.subcategory-list .subcategory-list .category-link {
    font-size: 11px;
    padding-left: 12px;
    min-height: 24px;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 16px;
    margin-left: var(--sidebar-width);
    overflow-y: auto;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.content-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.content-info {
    color: var(--text-secondary);
    font-size: 14px;
}

/* 网站容器 */
.sites-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 16px;
    margin-bottom: 32px;
    transition: gap var(--transition-normal), grid-template-columns var(--transition-normal);
}

/* 当容器包含分组时，覆盖网格布局 */
.sites-container:has(.category-group) {
    display: block;
}

/* 兼容性回退：当浏览器不支持 :has() 时 */
.sites-container.grouped-layout {
    display: block !important;
}

/* 瀑布流布局样式 */
.sites-container.waterfall-layout {
    display: block !important;
}

/* 分类分区样式 */
.category-section {
    margin-bottom: 48px;
}

.category-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 16px 0 12px 0;
    border-bottom: 2px solid var(--border-color);
    position: relative;
    background: linear-gradient(135deg, var(--surface-color) 0%, transparent 100%);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    margin-left: -8px;
    margin-right: -8px;
    padding-left: 16px;
    padding-right: 16px;
}

.category-section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
}

.category-section-icon {
    font-size: 28px;
}

/* 内容区分类组标题中的图标 */
.category-group-title .category-icon {
    font-size: 16px;
}

.category-section-count {
    background-color: var(--surface-color);
    color: var(--text-secondary);
    padding: 4px 12px;
    border-radius: var(--radius-lg);
    font-size: 14px;
    font-weight: 500;
}

.category-section-sites {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 16px;
}

/* 多级分类样式 */
.category-section-level-1 {
    margin-top: 32px;
    margin-bottom: 32px;
}

.category-section-level-2 {
    margin-top: 24px;
    margin-bottom: 24px;
}

.category-section-level-3 {
    margin-top: 16px;
    margin-bottom: 16px;
}

/* 子分类标题样式 */
.category-subsection-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.category-subsubsection-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
}

/* 子分类图标大小调整 */
.category-subsection-title .category-section-icon {
    font-size: 22px;
}

.category-subsubsection-title .category-section-icon {
    font-size: 20px;
}

/* 子分类头部样式调整 */
.category-section-level-1 .category-section-header {
    padding: 12px 0 10px 0;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--surface-color) 0%, transparent 100%);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    margin-left: -6px;
    margin-right: -6px;
    padding-left: 12px;
    padding-right: 12px;
}

.category-section-level-2 .category-section-header {
    padding: 10px 0 8px 0;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(var(--surface-color-rgb, 248, 250, 252), 0.5) 0%, transparent 100%);
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
    margin-left: -4px;
    margin-right: -4px;
    padding-left: 8px;
    padding-right: 8px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .category-section {
        margin-bottom: 32px;
    }

    .category-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 16px;
    }

    .category-section-title {
        font-size: 20px;
    }

    .category-subsection-title {
        font-size: 18px;
    }

    .category-subsubsection-title {
        font-size: 16px;
    }

    .category-section-icon {
        font-size: 24px;
    }

    .category-subsection-title .category-section-icon {
        font-size: 20px;
    }

    .category-subsubsection-title .category-section-icon {
        font-size: 18px;
    }

    .category-section-sites {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 12px;
    }

    /* 移动端多级分类间距调整 */
    .category-section-level-1 {
        margin-top: 24px;
        margin-bottom: 24px;
    }

    .category-section-level-2 {
        margin-top: 16px;
        margin-bottom: 16px;
    }

    .category-section-level-3 {
        margin-top: 12px;
        margin-bottom: 12px;
    }
}

@media (max-width: 480px) {
    .category-section-sites {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .category-section-title {
        font-size: 18px;
    }
}

/* 网站卡片 */
.site-card {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 16px;
    transition: all var(--transition-normal), padding var(--transition-normal), border-radius var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.site-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #8b5cf6);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.site-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-hover);
}

.site-card:hover::before {
    transform: scaleX(1);
}

.site-header {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 10px;
}

.site-icon {
    font-size: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--surface-color);
    border-radius: var(--radius-md);
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

/* 图片图标样式 */
.site-icon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: var(--radius-md);
    background-color: transparent;
}

/* 图标加载失败时的回退样式 */
.site-icon-fallback {
    font-size: 16px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.site-info {
    flex: 1;
    min-width: 0;
}

.site-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 3px 0;
    line-height: 1.3;
}

.site-url {
    font-size: 11px;
    color: var(--text-muted);
    word-break: break-all;
}

.site-type-badge {
    font-size: 10px;
    background-color: var(--accent-color-alpha);
    color: var(--accent-color);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-weight: 500;
    margin-left: auto;
}

/* 文档指示器样式 */
.doc-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background-color: var(--warning-color);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-left: auto;
    position: relative;
    z-index: 2;
}

.doc-indicator:hover {
    background-color: var(--warning-hover, #e09900);
    transform: scale(1.05);
}

.doc-indicator i {
    font-size: 9px;
}

.doc-indicator span {
    font-size: 9px;
    line-height: 1;
}

/* 紧凑模式下的文档指示器 */
.compact-view .doc-indicator {
    padding: 2px 4px;
    min-width: 16px;
    height: 16px;
    border-radius: 50%;
    justify-content: center;
}

.compact-view .doc-indicator span {
    display: none;
}

.compact-view .doc-indicator i {
    font-size: 8px;
}

/* Markdown 卡片样式 */
.site-card-markdown {
    border-left: 2px solid var(--accent-color);
    background: linear-gradient(135deg, var(--card-background) 0%, var(--surface-color) 100%);
}

.site-card-markdown:hover {
    border-left-color: var(--accent-hover);
    box-shadow: var(--shadow-md), 0 0 20px var(--accent-color-alpha);
}

.site-card-markdown .site-icon {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 外部链接卡片样式 - 基础样式更中性 */
.site-card-external {
    border-left: 2px solid var(--border-color);
    background: linear-gradient(135deg, var(--card-background) 0%, var(--surface-color) 100%);
}

.site-card-external:hover {
    border-left-color: var(--border-hover);
    box-shadow: var(--shadow-md);
}

/* 同时包含网址和文档的卡片样式 */
.site-card-both {
    border-left: 2px solid var(--primary-color);
    background: linear-gradient(135deg, var(--card-background) 0%, var(--surface-color) 100%);
    position: relative;
}

.site-card-both:hover {
    border-left-color: var(--primary-hover);
    box-shadow: var(--shadow-md), 0 0 15px rgba(59, 130, 246, 0.1);
}

.site-card-both::before {
    background: linear-gradient(90deg, var(--primary-color), var(--warning-color));
}

/* 无链接卡片样式 */
.site-card-no-link {
    border-left: 2px solid var(--text-muted);
    background: linear-gradient(135deg, var(--card-background) 0%, var(--surface-color) 100%);
    opacity: 0.8;
    cursor: default;
}

.site-card-no-link:hover {
    transform: none;
    box-shadow: var(--shadow-sm);
    border-left-color: var(--text-muted);
}

.site-card-no-link::before {
    display: none;
}

/* 卡片类型指示器 */
.site-card::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--text-muted);
    opacity: 0.3;
    transition: all var(--transition-fast);
}

.site-card-external::after {
    background-color: var(--success-color);
    opacity: 0.6;
}

.site-card-markdown::after {
    background-color: var(--warning-color);
    opacity: 0.6;
}

.site-card-both::after {
    background-color: var(--primary-color);
    opacity: 0.6;
}

.site-card-no-link::after {
    background-color: var(--text-muted);
    opacity: 0.3;
}

.site-card:hover::after {
    opacity: 1;
    transform: scale(1.2);
}

.site-description {
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.4;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 紧凑视图样式 */
.compact-view .site-card {
    padding: 8px 12px;
    border-radius: var(--radius-md);
    animation: compactModeIn 0.3s ease-out;
}

/* 视图切换动画 */
@keyframes compactModeIn {
    from {
        padding: 16px;
        border-radius: var(--radius-lg);
    }
    to {
        padding: 8px 12px;
        border-radius: var(--radius-md);
    }
}

.compact-view .site-header {
    margin-bottom: 0;
    gap: 8px;
}

.compact-view .site-icon {
    width: 24px;
    height: 24px;
    font-size: 16px;
}

.compact-view .site-info {
    flex: 1;
    min-width: 0;
}

.compact-view .site-title {
    font-size: 14px;
    margin: 0;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 紧凑视图下隐藏的元素 */
.compact-view .site-url,
.compact-view .site-description,
.compact-view .site-tags,
.compact-view .site-type-badge {
    display: none;
}

/* 紧凑视图下的网格布局调整 */
.compact-view.sites-container {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
}

.compact-view .category-group-sites {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
}

.compact-view .category-section-sites {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
}

/* 紧凑视图下的悬停效果调整 */
.compact-view .site-card:hover {
    transform: translateY(-2px);
}

/* 紧凑视图下的分类间距调整 */
.compact-view .category-section {
    margin-bottom: 24px;
}

.compact-view .category-group {
    margin-bottom: 16px;
}

.compact-view .category-section-header {
    margin-bottom: 16px;
}

.compact-view .category-group-header {
    margin-bottom: 8px;
}

/* 紧凑视图下的多级分类间距调整 */
.compact-view .category-section-level-1 {
    margin-top: 20px;
    margin-bottom: 20px;
}

.compact-view .category-section-level-2 {
    margin-top: 16px;
    margin-bottom: 16px;
}

.compact-view .category-section-level-3 {
    margin-top: 12px;
    margin-bottom: 12px;
}

/* 紧凑视图下的瀑布流布局间距调整 */
.compact-view.waterfall-layout .category-section {
    margin-bottom: 20px;
}

.compact-view.waterfall-layout .category-section .category-group-header {
    margin-bottom: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .compact-view.sites-container,
    .compact-view .category-group-sites,
    .compact-view .category-section-sites {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    /* 移动端紧凑视图分类间距调整 */
    .compact-view .category-section {
        margin-bottom: 20px;
    }

    .compact-view .category-group {
        margin-bottom: 12px;
    }

    .compact-view .category-section-header {
        margin-bottom: 12px;
    }

    .compact-view .category-group-header {
        margin-bottom: 6px;
    }

    /* 移动端紧凑视图多级分类间距 */
    .compact-view .category-section-level-1 {
        margin-top: 16px;
        margin-bottom: 16px;
    }

    .compact-view .category-section-level-2 {
        margin-top: 12px;
        margin-bottom: 12px;
    }

    .compact-view .category-section-level-3 {
        margin-top: 8px;
        margin-bottom: 8px;
    }
}

@media (max-width: 480px) {
    .compact-view.sites-container,
    .compact-view .category-group-sites,
    .compact-view .category-section-sites {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }

    .compact-view .site-card {
        padding: 6px 10px;
    }

    .compact-view .site-title {
        font-size: 13px;
    }

    /* 小屏幕紧凑视图分类间距进一步调整 */
    .compact-view .category-section {
        margin-bottom: 16px;
    }

    .compact-view .category-group {
        margin-bottom: 10px;
    }

    .compact-view .category-section-header {
        margin-bottom: 10px;
    }

    .compact-view .category-group-header {
        margin-bottom: 5px;
    }

    /* 小屏幕紧凑视图多级分类间距 */
    .compact-view .category-section-level-1 {
        margin-top: 12px;
        margin-bottom: 12px;
    }

    .compact-view .category-section-level-2 {
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .compact-view .category-section-level-3 {
        margin-top: 6px;
        margin-bottom: 6px;
    }
}

.site-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
}

.site-tag {
    background-color: var(--surface-color);
    color: var(--text-secondary);
    padding: 1px 6px;
    border-radius: var(--radius-sm);
    font-size: 10px;
    font-weight: 500;
}

/* 加载状态 */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state h3 {
    margin: 20px 0 10px;
    color: var(--text-primary);
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

/* 移动端侧边栏遮罩 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* 分类分组样式 */
.category-group {
    margin-bottom: 24px;
}

.category-group:last-child {
    margin-bottom: 0;
}

.category-group-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 2px solid var(--primary-color);
}

.category-group-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}



.category-group-count {
    font-size: 14px;
    color: var(--text-secondary);
    background-color: var(--surface-color);
    padding: 4px 12px;
    border-radius: var(--radius-md);
    font-weight: 500;
}

.category-group-sites {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 16px;
}

/* 多级分组样式 */
.category-group.parent-group {
    margin-bottom: 40px;
    padding: 16px;
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(var(--surface-color-rgb, 248, 250, 252), 0.3) 100%);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.category-group.parent-group:last-child {
    margin-bottom: 0;
}

.category-group-header.parent-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 3px solid var(--primary-color);
}

.category-group-title.parent-title {
    font-size: 22px;
    font-weight: 700;
    color: var(--primary-color);
}

.category-group.child-group {
    margin-bottom: 20px;
    margin-left: 0;
    padding: 12px;
    background: var(--background-color);
    border-radius: var(--radius-md);
    border: 1px solid rgba(var(--border-color-rgb, 226, 232, 240), 0.6);
}

.category-group.child-group:last-child {
    margin-bottom: 0;
}

.category-group-header.child-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.category-group-title.child-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

/* 瀑布流布局下的分类区域样式 */
.waterfall-layout .category-section {
    margin-bottom: 32px;
}

.waterfall-layout .category-section .category-group-header {
    margin-bottom: 16px;
}

/* 瀑布流布局下只有标题没有网站的分类区域 */
.waterfall-layout .category-section-header-only {
    margin-bottom: 16px;
}

.waterfall-layout .category-section-header-only .category-group-header {
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 2px solid var(--primary-color);
}

/* 分类层级缩进样式 */
.category-indent-1 {
    margin-left: 20px !important;
}

.category-indent-2 {
    margin-left: 40px !important;
}

.category-indent-3 {
    margin-left: 60px !important;
}

/* 瀑布流布局下的多级分类样式调整 */
.waterfall-layout .category-section-level-1 .category-group-header {
    padding: 12px 0 10px 0;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--surface-color) 0%, transparent 100%);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    margin-right: -6px;
    padding-left: 12px;
    padding-right: 12px;
}

.waterfall-layout .category-section-level-2 .category-group-header {
    padding: 10px 0 8px 0;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(var(--surface-color-rgb, 248, 250, 252), 0.5) 0%, transparent 100%);
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
    margin-right: -4px;
    padding-left: 8px;
    padding-right: 8px;
}

/* 分组中的网站卡片保持与常规卡片一致的样式 */

/* 多级分组响应式样式 */
@media (max-width: 768px) {
    .category-group.parent-group {
        padding: 12px;
        margin-bottom: 32px;
    }

    .category-group-header.parent-header {
        margin-bottom: 16px;
        padding-bottom: 10px;
    }

    .category-group-title.parent-title {
        font-size: 20px;
    }

    .category-group.child-group {
        padding: 10px;
        margin-bottom: 16px;
    }

    .category-group-header.child-header {
        margin-bottom: 10px;
        padding-bottom: 6px;
    }

    .category-group-title.child-title {
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .category-group.parent-group {
        padding: 10px;
        margin-bottom: 28px;
    }

    .category-group-header.parent-header {
        margin-bottom: 14px;
        padding-bottom: 8px;
    }

    .category-group-title.parent-title {
        font-size: 18px;
    }

    .category-group.child-group {
        padding: 8px;
        margin-bottom: 14px;
    }

    .category-group-header.child-header {
        margin-bottom: 8px;
        padding-bottom: 5px;
    }

    .category-group-title.child-title {
        font-size: 14px;
    }
}
.category-empty {
    padding: 20px 16px;
    text-align: center;
    color: var(--text-muted);
    font-size: 13px;
    font-style: italic;
}

/* 加载状态 */
.category-loading {
    padding: 16px;
    text-align: center;
    color: var(--text-muted);
    font-size: 13px;
}

.category-loading::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 交互反馈动画 - 已禁用以提升性能 */
.interaction-feedback {
    /* animation: pulseInteraction 0.2s ease-out; */
}

/*
@keyframes pulseInteraction {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}
*/

/* 分类项微交互效果 - 已禁用以提升性能 */
.category-link {
    position: relative;
    /* overflow: hidden; */
}

/*
.category-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.category-link:hover::before {
    left: 100%;
}

.category-link.active::before {
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
}
*/

/* 分类图标旋转效果 - 已禁用以提升性能 */
/*
.category-link:active .category-icon {
    animation: iconBounce 0.3s ease;
}

@keyframes iconBounce {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}
*/

/* 分类计数器跳动效果 - 已禁用以提升性能 */
/*
.category-count {
    transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.category-link:hover .category-count {
    animation: countBounce 0.4s ease;
}

@keyframes countBounce {
    0%, 100% {
        transform: scale(1.05);
    }
    50% {
        transform: scale(1.15);
    }
}
*/

/* 焦点状态优化 */
.category-link:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-color), 
                0 0 0 4px rgba(59, 130, 246, 0.1);
    z-index: 10;
}

.category-link:focus-visible {
    box-shadow: 0 0 0 2px var(--primary-color), 
                0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* 旧的连接线动画已被新的树状设计替换 */

/* 移动端侧边栏动画优化 */
@media (max-width: 767px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 999; /* 确保移动端侧边栏在遮罩层之上 */
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .sidebar-overlay {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
        z-index: 998; /* 确保遮罩层在侧边栏之下 */
    }

    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }
}

/* 加载时的渐入动画 - 已禁用以提升性能 */
.category-list {
    /* animation: fadeInUp 0.5s ease-out; */
}

/*
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
*/

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .category-link {
        border: 2px solid transparent;
    }
    
    .category-link:hover,
    .category-link:focus {
        border-color: var(--text-primary);
    }
    
    .category-link.active {
        border-color: var(--primary-color);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .category-link::before {
        display: none;
    }
    
    .subcategory-list::before {
        transform: scaleY(1);
        transition: none;
    }
}

/* 深度层级指示器 - 已整合到主要树状结构设计中 */
.subcategory-list .subcategory-list {
    position: relative;
}

/* 滚动条样式优化 */
.sidebar::-webkit-scrollbar {
    width: 4px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
    transition: background 0.2s ease, transform 0.2s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
    transform: scaleX(1.5);
}

/* Firefox 滚动条样式 */
.sidebar {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

/* 空状态和加载状态动画增强 - 已禁用以提升性能 */
.category-empty,
.category-loading {
    /* animation: fadeIn 0.3s ease; */
}

/*
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
*/

/* 分类项渐显动画 - 已禁用以提升性能 */
.category-item {
    /* animation: slideInLeft 0.3s ease-out; */
    /* animation-fill-mode: both; */
}

/* 移动端筛选器优化 */
@media (max-width: 768px) {
    .search-filters {
        left: 8px;
        right: 8px;
        margin-top: 8px;
        padding: 12px;
    }

    /* 自定义下拉选择器移动端优化 */
    .custom-select-trigger {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 12px;
        min-height: 44px; /* 确保触摸目标足够大 */
    }

    .custom-select-options {
        max-height: 250px; /* 限制移动端高度 */
    }

    .custom-select-option {
        padding: 12px;
        font-size: 16px;
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    /* 原生select移动端优化 */
    .filter-select {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 12px 32px 12px 12px;
        min-height: 44px; /* 确保触摸目标足够大 */
    }

    .filter-label {
        font-size: 13px;
        margin-bottom: 4px;
    }

    .tag-filters {
        gap: 8px;
    }

    .tag-filter {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .search-filters-content {
        gap: 20px;
    }
}

/* 小屏幕设备进一步优化 */
@media (max-width: 480px) {
    .search-filters {
        left: 4px;
        right: 4px;
        padding: 8px;
    }

    /* 自定义下拉选择器小屏幕优化 */
    .custom-select-trigger {
        padding: 10px;
        font-size: 16px;
        min-height: 40px;
    }

    .custom-select-option {
        padding: 10px;
        font-size: 15px;
        min-height: 40px;
    }

    /* 原生select小屏幕优化 */
    .filter-select {
        font-size: 16px;
        padding: 10px 28px 10px 10px;
    }

    .tag-filter {
        padding: 6px 10px;
        font-size: 12px;
        min-height: 32px;
    }
}

/*
.category-item:nth-child(1) { animation-delay: 0.05s; }
.category-item:nth-child(2) { animation-delay: 0.1s; }
.category-item:nth-child(3) { animation-delay: 0.15s; }
.category-item:nth-child(4) { animation-delay: 0.2s; }
.category-item:nth-child(5) { animation-delay: 0.25s; }
.category-item:nth-child(n+6) { animation-delay: 0.3s; }

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
*/

/* 卡片快捷键功能样式 */
.card-hovered {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1), var(--shadow-lg) !important;
    transform: translateY(-2px) !important;
}

.card-shortcut-hint {
    position: absolute;
    bottom: 8px;
    left: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: var(--radius-sm);
    padding: 6px 8px;
    font-size: 11px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.2s ease;
    z-index: 10;
    pointer-events: none;
}

.card-shortcut-hint.show {
    opacity: 1;
    transform: translateY(0);
}

.shortcut-hint-content {
    display: flex;
    align-items: center;
    gap: 6px;
    justify-content: center;
}

.shortcut-hint-content i {
    font-size: 10px;
    opacity: 0.8;
}

.shortcut-hint-content span {
    font-weight: 500;
    line-height: 1.2;
}

/* 操作提示样式 */
.operation-hint {
    position: fixed;
    top: 80px;
    right: 20px;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 12px 16px;
    box-shadow: var(--shadow-lg);
    z-index: 10000;
    max-width: 300px;
    min-width: 200px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.operation-hint.show {
    opacity: 1;
    transform: translateX(0);
}

.operation-hint .hint-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.operation-hint .hint-content i {
    font-size: 14px;
    flex-shrink: 0;
}

.operation-hint .hint-content span {
    flex: 1;
    line-height: 1.4;
    color: var(--text-primary);
    font-size: 13px;
}

/* 操作提示类型样式 */
.operation-hint-success {
    border-left: 4px solid var(--success-color);
}

.operation-hint-success .hint-content i {
    color: var(--success-color);
}

.operation-hint-info {
    border-left: 4px solid #3b82f6;
}

.operation-hint-info .hint-content i {
    color: #3b82f6;
}

.operation-hint-warning {
    border-left: 4px solid var(--warning-color);
}

.operation-hint-warning .hint-content i {
    color: var(--warning-color);
}

.operation-hint-error {
    border-left: 4px solid var(--danger-color);
}

.operation-hint-error .hint-content i {
    color: var(--danger-color);
}

/* 紧凑模式下的快捷键提示调整 */
.compact-view .card-shortcut-hint {
    bottom: 4px;
    left: 4px;
    right: 4px;
    padding: 4px 6px;
    font-size: 10px;
}

.compact-view .shortcut-hint-content {
    gap: 4px;
}

.compact-view .shortcut-hint-content i {
    font-size: 9px;
}

/* 移动端快捷键提示优化 */
@media (max-width: 768px) {
    .card-shortcut-hint {
        bottom: 6px;
        left: 6px;
        right: 6px;
        padding: 5px 7px;
        font-size: 10px;
    }

    .operation-hint {
        top: 70px;
        right: 10px;
        left: 10px;
        max-width: none;
        min-width: auto;
    }

    .operation-hint .hint-content span {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .card-shortcut-hint {
        bottom: 4px;
        left: 4px;
        right: 4px;
        padding: 4px 6px;
        font-size: 9px;
    }

    .shortcut-hint-content {
        gap: 3px;
    }

    .shortcut-hint-content i {
        font-size: 8px;
    }

    .operation-hint {
        top: 65px;
        right: 8px;
        left: 8px;
        padding: 10px 12px;
    }
}

/* 深色主题下的快捷键提示优化 */
[data-theme*="dark"] .card-shortcut-hint {
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
}

[data-theme*="dark"] .shortcut-hint-content i {
    opacity: 0.7;
}

/* Toast 通知样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 16px;
    box-shadow: var(--shadow-lg);
    z-index: 10000;
    max-width: 400px;
    min-width: 300px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.toast-content i {
    font-size: 18px;
    margin-top: 2px;
    flex-shrink: 0;
}

.toast-content span {
    flex: 1;
    line-height: 1.4;
    color: var(--text-primary);
}

/* Toast 类型样式 */
.toast-success {
    border-left: 4px solid #28a745;
}

.toast-success .toast-content i {
    color: #28a745;
}

.toast-error {
    border-left: 4px solid #dc3545;
}

.toast-error .toast-content i {
    color: #dc3545;
}

.toast-warning {
    border-left: 4px solid #ffc107;
}

.toast-warning .toast-content i {
    color: #ffc107;
}

.toast-info {
    border-left: 4px solid #17a2b8;
}

.toast-info .toast-content i {
    color: #17a2b8;
}

/* 数据管理下拉菜单样式 */
.data-management-dropdown {
    position: relative;
    display: inline-block;
}

/* 数据管理按钮美化样式 */
.data-management-btn {
    width: auto !important;
    min-width: 40px;
    padding: 8px 12px !important;
    background-color: var(--surface-color) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    gap: 6px;
}

.data-management-btn:hover {
    background-color: var(--surface-hover) !important;
    border-color: var(--border-hover) !important;
    color: var(--text-primary) !important;
}

.data-management-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-color);
}

.data-management-btn i:first-child {
    font-size: 14px;
    color: var(--primary-color);
}

.data-name {
    font-size: 13px;
    font-weight: 500;
    margin: 0 2px;
}

.data-dropdown-arrow {
    font-size: 10px;
    transition: transform 0.2s ease;
    opacity: 0.7;
}

.data-management-dropdown.show .data-dropdown-arrow {
    transform: rotate(180deg);
}

.data-management-dropdown.show .data-management-btn {
    background-color: var(--surface-hover) !important;
    border-color: var(--border-hover) !important;
}

.data-management-dropdown .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.12), 0 4px 8px rgba(0,0,0,0.08);
    min-width: 180px;
    z-index: 1000;
    padding: 8px 0;
    margin-top: 8px;
    backdrop-filter: blur(10px);
    animation: dropdownFadeIn 0.2s ease-out;
}

.data-management-dropdown.show .dropdown-menu {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 18px;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border-radius: 8px;
    margin: 2px 8px;
    width: calc(100% - 16px);
}

.dropdown-item:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
    transform: translateX(2px);
}

.dropdown-item i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
    font-size: 14px;
    opacity: 0.8;
}

.dropdown-item:hover i {
    opacity: 1;
    color: var(--primary-color);
}

.dropdown-item.text-danger {
    color: #dc3545;
}

.dropdown-item.text-danger:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.dropdown-item.text-danger:hover i {
    color: #dc3545;
}

.dropdown-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    margin: 8px 12px;
    border: none;
}

/* 下拉菜单动画 */
@keyframes dropdownFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 确保下拉菜单在移动设备上也能正常显示 */
@media (max-width: 767px) {
    .data-management-dropdown .dropdown-menu {
        right: -10px;
        min-width: 160px;
    }

    .dropdown-item {
        padding: 10px 14px;
        font-size: 13px;
        margin: 1px 6px;
        width: calc(100% - 12px);
    }

    .data-management-btn {
        padding: 6px 10px !important;
    }

    .data-name {
        font-size: 12px;
    }
}

/* 时间范围通知样式 */
.time-range-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 10000;
    max-width: 350px;
    min-width: 280px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.time-range-notification-content {
    padding: 16px;
}

.notification-title {
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.notification-message {
    font-size: 14px;
    line-height: 1.4;
    color: var(--text-secondary);
}

/* 数据状态卡片样式 */
.data-status-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.data-status-card {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    transform: scale(0.9);
    transition: all var(--transition-normal);
    max-width: 480px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
}

.data-status-card.show {
    opacity: 1;
    transform: scale(1);
}

.data-status-card.status-healthy {
    border-left: 4px solid var(--success-color);
}

.data-status-card.status-warning {
    border-left: 4px solid var(--warning-color);
}

.data-status-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--surface-color) 0%, var(--card-background) 100%);
}

.data-status-header .status-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.status-healthy .status-icon {
    background: linear-gradient(135deg, #28a745, #10b981);
    color: white;
}

.status-warning .status-icon {
    background: linear-gradient(135deg, #ffc107, #f59e0b);
    color: white;
}

.data-status-header .status-title {
    flex: 1;
}

.data-status-header .status-title h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.data-status-header .status-title p {
    margin: 4px 0 0;
    font-size: 14px;
    color: var(--text-muted);
}

.data-status-header .close-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--surface-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-muted);
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.data-status-header .close-btn:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.1);
}

.data-status-body {
    padding: 20px 24px;
    max-height: 400px;
    overflow-y: auto;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-light);
}

.status-item:last-child {
    border-bottom: none;
}

.status-item-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-md);
    background: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    flex-shrink: 0;
}

.status-item-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-item-label {
    font-weight: 500;
    color: var(--text-primary);
}

.status-item-value {
    font-weight: 600;
    color: var(--text-secondary);
}

.status-item-value.status-ok {
    color: #28a745;
}

.status-item-value.status-error {
    color: #dc3545;
}

.data-status-footer {
    padding: 12px 24px;
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    text-align: center;
    transition: opacity var(--transition-normal);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .data-status-card {
        width: 95%;
        max-width: none;
    }

    .data-status-header {
        padding: 16px 20px 12px;
    }

    .data-status-body {
        padding: 16px 20px;
    }

    .data-status-footer {
        padding: 10px 20px;
    }

    .status-item {
        gap: 12px;
    }

    .status-item-icon {
        width: 32px;
        height: 32px;
    }
}

.search-no-results {
    padding: 15px;
    text-align: center;
    color: var(--text-muted);
    font-size: 13px;
    font-style: italic;
}