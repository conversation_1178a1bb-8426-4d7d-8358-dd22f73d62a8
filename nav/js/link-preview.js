/**
 * 链接预览管理器
 * 负责在鼠标悬停卡片时显示链接信息到浏览器状态栏
 */
class LinkPreviewManager {
    constructor() {
        this.isSupported = this.checkBrowserSupport();
        this.maxUrlLength = 80; // URL最大显示长度
        this.currentPreview = null; // 当前显示的预览信息
        
        console.log('链接预览管理器初始化完成', { 
            isSupported: this.isSupported,
            maxUrlLength: this.maxUrlLength 
        });
    }

    /**
     * 检查浏览器是否支持状态栏操作
     * @returns {boolean} 是否支持
     */
    checkBrowserSupport() {
        // 现代浏览器出于安全考虑，通常不允许修改状态栏
        // 我们将使用title属性作为替代方案
        return true;
    }

    /**
     * 显示链接预览
     * @param {Element} cardElement 卡片元素
     */
    showLinkPreview(cardElement) {
        if (!cardElement) {
            console.warn('链接预览：卡片元素为空');
            return;
        }

        const url = cardElement.dataset.url;
        const markdownFile = cardElement.dataset.markdownFile;
        
        // 构建预览文本
        const previewText = this.buildPreviewText(url, markdownFile);
        
        if (previewText) {
            // 设置卡片的title属性来显示链接信息
            this.setCardTitle(cardElement, previewText);
            
            // 尝试设置状态栏（在支持的浏览器中）
            this.setStatusBar(previewText);
            
            this.currentPreview = {
                element: cardElement,
                text: previewText
            };
            
            console.log('显示链接预览:', previewText);
        }
    }

    /**
     * 清除链接预览
     */
    clearLinkPreview() {
        if (this.currentPreview) {
            // 清除title属性
            this.clearCardTitle(this.currentPreview.element);
            
            // 清除状态栏
            this.clearStatusBar();
            
            console.log('清除链接预览:', this.currentPreview.text);
            this.currentPreview = null;
        }
    }

    /**
     * 构建预览文本
     * @param {string} url 跳转链接
     * @param {string} markdownFile markdown文件路径
     * @returns {string} 预览文本
     */
    buildPreviewText(url, markdownFile) {
        const hasUrl = url && url.trim() !== '';
        const hasMarkdown = markdownFile && markdownFile.trim() !== '';

        if (!hasUrl && !hasMarkdown) {
            return null; // 没有链接信息
        }

        const parts = [];

        // 处理跳转链接
        if (hasUrl) {
            const formattedUrl = this.formatUrl(url);
            parts.push(formattedUrl);
        }

        // 处理markdown链接
        if (hasMarkdown) {
            const formattedMarkdown = this.formatMarkdownPath(markdownFile);
            parts.push(`📄 ${formattedMarkdown}`);
        }

        return parts.join(' | ');
    }

    /**
     * 格式化URL显示
     * @param {string} url 原始URL
     * @returns {string} 格式化后的URL
     */
    formatUrl(url) {
        if (!url) return '';
        
        try {
            // 确保URL有协议前缀
            const fullUrl = url.startsWith('http') ? url : `https://${url}`;
            const urlObj = new URL(fullUrl);
            
            // 构建显示文本：域名 + 路径（如果有）
            let displayText = urlObj.hostname;
            
            if (urlObj.pathname && urlObj.pathname !== '/') {
                displayText += urlObj.pathname;
            }
            
            // 添加查询参数（如果有且不太长）
            if (urlObj.search && displayText.length < this.maxUrlLength - 10) {
                displayText += urlObj.search;
            }
            
            // 截断过长的URL
            if (displayText.length > this.maxUrlLength) {
                displayText = displayText.substring(0, this.maxUrlLength - 3) + '...';
            }
            
            return displayText;
        } catch (error) {
            console.warn('URL格式化失败:', url, error);
            // 如果URL解析失败，直接截断显示
            return url.length > this.maxUrlLength ? 
                url.substring(0, this.maxUrlLength - 3) + '...' : url;
        }
    }

    /**
     * 格式化Markdown文件路径显示
     * @param {string} markdownFile markdown文件路径
     * @returns {string} 格式化后的路径
     */
    formatMarkdownPath(markdownFile) {
        if (!markdownFile) return '';
        
        // 提取文件名（去掉路径和扩展名）
        const fileName = markdownFile.split('/').pop().replace(/\.md$/i, '');
        
        // 如果文件名太长，进行截断
        if (fileName.length > 30) {
            return fileName.substring(0, 27) + '...';
        }
        
        return fileName;
    }

    /**
     * 设置卡片的title属性
     * @param {Element} cardElement 卡片元素
     * @param {string} text 预览文本
     */
    setCardTitle(cardElement, text) {
        // 保存原始title（如果有）
        if (!cardElement.dataset.originalTitle) {
            cardElement.dataset.originalTitle = cardElement.title || '';
        }
        
        // 设置新的title
        cardElement.title = text;
    }

    /**
     * 清除卡片的title属性
     * @param {Element} cardElement 卡片元素
     */
    clearCardTitle(cardElement) {
        if (cardElement && cardElement.dataset.originalTitle !== undefined) {
            // 恢复原始title
            cardElement.title = cardElement.dataset.originalTitle;
            delete cardElement.dataset.originalTitle;
        }
    }

    /**
     * 设置浏览器状态栏（在支持的浏览器中）
     * @param {string} text 状态栏文本
     */
    setStatusBar(text) {
        try {
            // 现代浏览器通常不支持修改状态栏
            // 这里保留接口，以防未来有其他实现方式
            if (window.status !== undefined) {
                window.status = text;
            }
        } catch (error) {
            // 忽略状态栏设置错误
            console.debug('状态栏设置失败（这是正常的）:', error.message);
        }
    }

    /**
     * 清除浏览器状态栏
     */
    clearStatusBar() {
        try {
            if (window.status !== undefined) {
                window.status = '';
            }
        } catch (error) {
            // 忽略状态栏清除错误
            console.debug('状态栏清除失败（这是正常的）:', error.message);
        }
    }

    /**
     * 销毁链接预览管理器
     */
    destroy() {
        this.clearLinkPreview();
        console.log('链接预览管理器已销毁');
    }
}

// 导出到全局作用域
window.LinkPreviewManager = LinkPreviewManager;
